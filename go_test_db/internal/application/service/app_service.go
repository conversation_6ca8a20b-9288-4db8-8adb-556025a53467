package service

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"db_test/internal/application/handler"
	"db_test/internal/application/middleware"
	"db_test/internal/domain/usecase"
	"db_test/internal/infrastructure/config"
	"db_test/internal/infrastructure/database"
	"db_test/internal/infrastructure/web"
)

// AppService manages the application lifecycle
type AppService struct {
	config *config.Config
	server *web.Server
	db     *sql.DB
}

// NewAppService creates a new application service
func NewAppService() *AppService {
	return &AppService{
		config: config.Load(),
	}
}

// Start starts the application
func (a *AppService) Start() error {
	// Initialize database
	if err := a.initDatabase(); err != nil {
		return fmt.Errorf("failed to initialize database: %w", err)
	}

	// Initialize server
	if err := a.initServer(); err != nil {
		return fmt.Errorf("failed to initialize server: %w", err)
	}

	// Start server in a goroutine
	go func() {
		if err := a.server.Start(); err != nil {
			log.Printf("Server error: %v", err)
		}
	}()

	// Wait for interrupt signal
	a.waitForShutdown()

	return nil
}

// initDatabase initializes the database connection
func (a *AppService) initDatabase() error {
	dbConfig := database.Config{
		Host:     a.config.Database.Host,
		Port:     a.config.Database.Port,
		User:     a.config.Database.User,
		Password: a.config.Database.Password,
		DBName:   a.config.Database.DBName,
		SSLMode:  a.config.Database.SSLMode,
	}

	db, err := database.NewConnection(dbConfig)
	if err != nil {
		return err
	}

	// Create tables
	if err := database.CreateTables(db); err != nil {
		return err
	}

	a.db = db
	log.Println("Database connected successfully")
	return nil
}

// initServer initializes the HTTP server and routes
func (a *AppService) initServer() error {
	serverConfig := web.Config{
		Port:         a.config.Server.Port,
		ReadTimeout:  a.config.Server.ReadTimeout,
		WriteTimeout: a.config.Server.WriteTimeout,
		IdleTimeout:  a.config.Server.IdleTimeout,
	}

	a.server = web.NewServer(serverConfig)

	// Initialize dependencies
	userRepo := database.NewUserRepository(a.db)
	userUseCase := usecase.NewUserUseCase(userRepo)
	userHandler := handler.NewUserHandler(userUseCase)

	// Setup routes
	router := a.server.GetRouter()
	
	// Apply middleware
	router.Use(middleware.Logging)
	router.Use(middleware.CORS)

	// Routes
	router.HandleFunc("/hello", userHandler.HelloWorld).Methods("GET")
	router.HandleFunc("/users", userHandler.CreateUser).Methods("POST")
	router.HandleFunc("/users", userHandler.ListUsers).Methods("GET")
	router.HandleFunc("/users/{id}", userHandler.GetUser).Methods("GET")

	log.Printf("Server initialized on port %d", a.config.Server.Port)
	return nil
}

// waitForShutdown waits for interrupt signal and gracefully shuts down
func (a *AppService) waitForShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := a.server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	if a.db != nil {
		a.db.Close()
		log.Println("Database connection closed")
	}

	log.Println("Server exited")
}
